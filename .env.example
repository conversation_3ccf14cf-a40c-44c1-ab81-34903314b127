# Application Configuration
APP_NAME="AI Image Generator"
APP_URL="http://localhost/aibulky"
APP_ENV="development"
APP_DEBUG=true

# Database Configuration
DB_PATH="database/app.db"

# Email Configuration (SMTP)
MAIL_HOST="smtp.gmail.com"
MAIL_PORT=587
MAIL_USERNAME=""
MAIL_PASSWORD=""
MAIL_ENCRYPTION="tls"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="AI Image Generator"

# Stripe Payment Configuration
STRIPE_PUBLIC_KEY=""
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# AI Provider Configuration (Users bring their own API keys)
# These are just for documentation/testing purposes
DEFAULT_AI_PROVIDER="together_ai"
SUPPORTED_PROVIDERS="together_ai,runware,replicate,fal"

# Security
SESSION_LIFETIME=7200
CSRF_TOKEN_LIFETIME=3600
ENCRYPTION_KEY=""

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES="txt,csv"
UPLOAD_PATH="storage/uploads/"
GENERATED_IMAGES_PATH="storage/generated/"

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Admin Settings
ADMIN_EMAIL="<EMAIL>"
SITE_MAINTENANCE=false
