<?php

class Config {
    private static $config = [];
    
    public static function load() {
        // Load environment variables
        if (file_exists(__DIR__ . '/../.env')) {
            $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) {
                    continue;
                }
                list($name, $value) = explode('=', $line, 2);
                $_ENV[trim($name)] = trim($value, '"\'');
            }
        }
        
        // Set default configuration
        self::$config = [
            'app' => [
                'name' => $_ENV['APP_NAME'] ?? 'AI Image Generator',
                'url' => $_ENV['APP_URL'] ?? 'http://localhost',
                'env' => $_ENV['APP_ENV'] ?? 'development',
                'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
            ],
            'database' => [
                'path' => $_ENV['DB_PATH'] ?? 'database/app.db',
            ],
            'mail' => [
                'host' => $_ENV['MAIL_HOST'] ?? '',
                'port' => $_ENV['MAIL_PORT'] ?? 587,
                'username' => $_ENV['MAIL_USERNAME'] ?? '',
                'password' => $_ENV['MAIL_PASSWORD'] ?? '',
                'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
                'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '',
                'from_name' => $_ENV['MAIL_FROM_NAME'] ?? '',
            ],
            'stripe' => [
                'public_key' => $_ENV['STRIPE_PUBLIC_KEY'] ?? '',
                'secret_key' => $_ENV['STRIPE_SECRET_KEY'] ?? '',
                'webhook_secret' => $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '',
            ],
            'ai_providers' => [
                'together_ai' => $_ENV['TOGETHER_AI_API_KEY'] ?? '',
                'runware' => $_ENV['RUNWARE_API_KEY'] ?? '',
                'replicate' => $_ENV['REPLICATE_API_TOKEN'] ?? '',
                'fal' => $_ENV['FAL_API_KEY'] ?? '',
            ],
            'security' => [
                'session_lifetime' => $_ENV['SESSION_LIFETIME'] ?? 7200,
                'csrf_token_lifetime' => $_ENV['CSRF_TOKEN_LIFETIME'] ?? 3600,
                'encryption_key' => $_ENV['ENCRYPTION_KEY'] ?? '',
            ],
            'upload' => [
                'max_file_size' => $_ENV['MAX_FILE_SIZE'] ?? 10485760,
                'allowed_types' => explode(',', $_ENV['ALLOWED_FILE_TYPES'] ?? 'txt,csv'),
                'upload_path' => $_ENV['UPLOAD_PATH'] ?? 'storage/uploads/',
                'generated_path' => $_ENV['GENERATED_IMAGES_PATH'] ?? 'storage/generated/',
            ],
            'rate_limit' => [
                'requests' => $_ENV['RATE_LIMIT_REQUESTS'] ?? 100,
                'window' => $_ENV['RATE_LIMIT_WINDOW'] ?? 3600,
            ],
        ];
        
        // Create necessary directories
        self::createDirectories();
    }
    
    public static function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    private static function createDirectories() {
        $directories = [
            'storage',
            'storage/uploads',
            'storage/generated',
            'storage/logs',
            'database'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
}
