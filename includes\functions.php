<?php

/**
 * Global utility functions
 */

function redirect($url, $statusCode = 302) {
    header("Location: $url", true, $statusCode);
    exit();
}

function asset($path) {
    $baseUrl = Config::get('app.url');
    return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
}

function url($path = '') {
    $baseUrl = Config::get('app.url');
    return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
}

function flash($key, $message = null) {
    if ($message === null) {
        $message = $_SESSION['flash'][$key] ?? null;
        unset($_SESSION['flash'][$key]);
        return $message;
    }
    $_SESSION['flash'][$key] = $message;
}

function old($key, $default = '') {
    return $_SESSION['old'][$key] ?? $default;
}

function clearOld() {
    unset($_SESSION['old']);
}

function setOld($data) {
    $_SESSION['old'] = $data;
}

function csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }

    $tokenAge = time() - $_SESSION['csrf_token_time'];
    $maxAge = Config::get('security.csrf_token_lifetime', 3600);

    if ($tokenAge > $maxAge) {
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}

function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function generate_random_string($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

function encrypt($data, $key = null) {
    if ($key === null) {
        $key = Config::get('security.encryption_key');
    }

    if (empty($key)) {
        throw new Exception('Encryption key not set');
    }

    $iv = random_bytes(16);
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    return base64_encode($iv . $encrypted);
}

function decrypt($data, $key = null) {
    if ($key === null) {
        $key = Config::get('security.encryption_key');
    }

    if (empty($key)) {
        throw new Exception('Encryption key not set');
    }

    $data = base64_decode($data);
    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

function format_bytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

function time_ago($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

function is_valid_image_dimensions($width, $height) {
    $validDimensions = [
        '512x512', '768x768', '1024x1024',
        '1024x768', '768x1024', '1152x896',
        '896x1152', '1216x832', '832x1216'
    ];
    return in_array($width . 'x' . $height, $validDimensions);
}

function get_supported_ai_providers() {
    return Config::get('ai_providers.supported', []);
}

function get_default_ai_provider() {
    return Config::get('ai_providers.default', 'together_ai');
}

function log_error($message, $context = []) {
    $logFile = 'storage/logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logMessage = "[$timestamp] $message$contextStr" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

function log_activity($userId, $action, $details = []) {
    $logFile = 'storage/logs/activity.log';
    $timestamp = date('Y-m-d H:i:s');
    $detailsStr = !empty($details) ? ' | Details: ' . json_encode($details) : '';
    $logMessage = "[$timestamp] User $userId: $action$detailsStr" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

function rate_limit_check($identifier, $action, $limit = null, $window = null) {
    if ($limit === null) {
        $limit = Config::get('rate_limit.requests', 100);
    }
    if ($window === null) {
        $window = Config::get('rate_limit.window', 3600);
    }

    $db = Database::getInstance();
    $resetTime = time() + $window;

    // Clean old entries
    $stmt = $db->prepare("DELETE FROM rate_limits WHERE reset_time < ?");
    $stmt->execute([time()]);

    // Check current attempts
    $stmt = $db->prepare("SELECT attempts FROM rate_limits WHERE identifier = ? AND action = ?");
    $stmt->execute([$identifier, $action]);
    $result = $stmt->fetch();

    if ($result) {
        if ($result['attempts'] >= $limit) {
            return false;
        }

        // Increment attempts
        $stmt = $db->prepare("UPDATE rate_limits SET attempts = attempts + 1 WHERE identifier = ? AND action = ?");
        $stmt->execute([$identifier, $action]);
    } else {
        // Create new entry
        $stmt = $db->prepare("INSERT INTO rate_limits (identifier, action, attempts, reset_time) VALUES (?, ?, 1, ?)");
        $stmt->execute([$identifier, $action, $resetTime]);
    }

    return true;
}
