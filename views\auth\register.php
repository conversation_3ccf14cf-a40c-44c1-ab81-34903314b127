<?php
ob_start();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold">Create Account</h2>
                        <p class="text-muted">Join thousands of creators using AI to generate amazing images</p>
                    </div>

                    <form method="POST" action="/register">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text"
                                       class="form-control form-control-lg"
                                       id="first_name"
                                       name="first_name"
                                       value="<?= htmlspecialchars(old('first_name')) ?>"
                                       required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text"
                                       class="form-control form-control-lg"
                                       id="last_name"
                                       name="last_name"
                                       value="<?= htmlspecialchars(old('last_name')) ?>"
                                       required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email"
                                   class="form-control form-control-lg"
                                   id="email"
                                   name="email"
                                   value="<?= htmlspecialchars(old('email')) ?>"
                                   required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password"
                                   class="form-control form-control-lg"
                                   id="password"
                                   name="password"
                                   required>
                            <div class="form-text">
                                Password must be at least 8 characters long.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">Confirm Password</label>
                            <input type="password"
                                   class="form-control form-control-lg"
                                   id="password_confirmation"
                                   name="password_confirmation"
                                   required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="/terms" target="_blank">Terms of Service</a>
                                and <a href="/privacy" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </button>

                        <div class="text-center">
                            <small class="text-muted">
                                By creating an account, you'll start with our free plan which includes
                                5 images per day and 50 images per month.
                            </small>
                        </div>
                    </form>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted">
                    Already have an account?
                    <a href="/login" class="text-decoration-none fw-bold">Sign in here</a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
$title = 'Register';
$bodyClass = 'auth-page';
include __DIR__ . '/../layout/app.php';
?>
