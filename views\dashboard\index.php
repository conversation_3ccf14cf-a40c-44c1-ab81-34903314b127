<?php
ob_start();
?>

<div class="container-fluid py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 fw-bold">Welcome back, <?= htmlspecialchars(Auth::user()['first_name']) ?>!</h1>
            <p class="text-muted">Here's what's happening with your AI image generation</p>
        </div>
    </div>

    <!-- API Keys Warning -->
    <?php if (!$hasApiKeys): ?>
        <div class="row mb-4">
            <div class="col">
                <div class="alert alert-warning d-flex align-items-center" role="alert">
                    <i class="fas fa-exclamation-triangle me-3"></i>
                    <div>
                        <strong>API Keys Required!</strong>
                        You need to configure your AI provider API keys before you can generate images.
                        <a href="/api-keys" class="alert-link ms-2">Configure API Keys</a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <!-- Current Plan -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Current Plan</h6>
                            <h4 class="mb-0"><?= htmlspecialchars($subscription['plan_name'] ?? 'Free') ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daily Usage -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Today's Usage</h6>
                            <h4 class="mb-0">
                                <?= $usage['daily'] ?> / <?= $subscription['daily_generations'] ?? 5 ?>
                            </h4>
                            <div class="progress mt-2" style="height: 4px;">
                                <?php
                                $dailyPercent = ($subscription['daily_generations'] ?? 5) > 0
                                    ? ($usage['daily'] / ($subscription['daily_generations'] ?? 5)) * 100
                                    : 0;
                                ?>
                                <div class="progress-bar bg-success" style="width: <?= min($dailyPercent, 100) ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Usage -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Monthly Usage</h6>
                            <h4 class="mb-0">
                                <?= $usage['monthly'] ?> / <?= $subscription['monthly_generations'] ?? 50 ?>
                            </h4>
                            <div class="progress mt-2" style="height: 4px;">
                                <?php
                                $monthlyPercent = ($subscription['monthly_generations'] ?? 50) > 0
                                    ? ($usage['monthly'] / ($subscription['monthly_generations'] ?? 50)) * 100
                                    : 0;
                                ?>
                                <div class="progress-bar bg-info" style="width: <?= min($monthlyPercent, 100) ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Rate -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Success Rate</h6>
                            <?php
                            $totalImages = $monthlyStats['total_images'] ?? 0;
                            $successRate = $totalImages > 0
                                ? round(($monthlyStats['successful_images'] / $totalImages) * 100, 1)
                                : 0;
                            ?>
                            <h4 class="mb-0"><?= $successRate ?>%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Usage Chart -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Daily Usage (Last 7 Days)</h5>
                </div>
                <div class="card-body">
                    <canvas id="usageChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="/generate" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic me-2"></i>Generate Image
                        </a>
                        <a href="/bulk-generate" class="btn btn-outline-primary">
                            <i class="fas fa-layer-group me-2"></i>Bulk Generate
                        </a>
                        <a href="/gallery" class="btn btn-outline-secondary">
                            <i class="fas fa-images me-2"></i>View Gallery
                        </a>
                        <?php if (!$hasApiKeys): ?>
                            <a href="/api-keys" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>Setup API Keys
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Images -->
    <?php if (!empty($recentImages)): ?>
        <div class="row mt-4">
            <div class="col">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Images</h5>
                        <a href="/gallery" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <?php foreach ($recentImages as $image): ?>
                                <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                    <div class="card border-0 shadow-sm">
                                        <img src="<?= htmlspecialchars($image['file_path']) ?>"
                                             class="card-img-top"
                                             alt="Generated image"
                                             style="height: 120px; object-fit: cover;">
                                        <div class="card-body p-2">
                                            <small class="text-muted d-block">
                                                <?= htmlspecialchars(substr($image['prompt'], 0, 30)) ?>...
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?= time_ago($image['created_at']) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Usage Chart
const ctx = document.getElementById('usageChart').getContext('2d');
const usageData = <?= json_encode($dailyUsage) ?>;

new Chart(ctx, {
    type: 'line',
    data: {
        labels: usageData.map(item => {
            const date = new Date(item.date);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }),
        datasets: [{
            label: 'Images Generated',
            data: usageData.map(item => item.usage),
            borderColor: '#0d6efd',
            backgroundColor: 'rgba(13, 110, 253, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
</script>

<?php
$content = ob_get_clean();
$title = 'Dashboard';
include __DIR__ . '/../layout/app.php';
?>
